/*                               -*- Mode: C -*-
 *
 * File            : bootkernel.c
 * Program/Library : RAYS Packages Manager
 * Description     :
 * Created: Thu Apr  3 15:48:18 2008
 * Author: Wei<PERSON><PERSON> Zhu
 * Mail            : <EMAIL>
 * Last Modified By : WeiPing Zhu
 * Last Modified On : Mon Apr 14 15:04:01 2008 (28800 CST)
 * Update Count : 51
 *
 *    Copyright 2007 Sun Wah Linux Limited
 *    addr: New World Center, No.88 Zhujiang Road Nanjing, Jiangsu Province, 210018, China
 *    tel :
 *    fax :
 *
 *
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/param.h>
#include <sys/types.h>
#include <sys/errno.h>
#include <sys/endian.h>

#include <pmon.h>
#include <exec.h>
#include <pmon/loaders/loadfn.h>

#ifdef __loongarch__
#include <machine/cpu.h>
#endif

#ifdef TCM2
#include <sys/dev/tcm/tcm2_sm3.h>
#endif

#ifdef LS2_SE
#include <time.h>
#include <sys/dev/tcm/tcm2_sm3.h>
#include <sys/dev/tcm/tcm2_metric.h>
#include <tcm-v2.h>
#include <tcm-common.h>
#include <machine/cpu.h>
#endif

extern int errno;                       /* global error number */
extern char *heaptop;

int boot_kernel(const char* path, int flags, void* flashaddr, unsigned long offset)
{
	int bootfd;
	int bootbigend;

	char buf[DLREC+1];
	long ep;
	int n;
#ifdef HAVE_FLASH
	size_t flashsize;
#endif

	if ((bootfd = open(path, O_RDONLY | O_NONBLOCK)) < 0) {
		perror(path);
		return -1;
	}

#ifdef LS2_SE
	{
		unsigned char digest[TCM2_DIGEST_LEN];
		unsigned long long hash_start, hash_end, hash_cycles;
		unsigned long hash_time_sec;

		printf("============LS2_SE: Starting security measurement for %s=============\n", path);

		hash_start = CPU_GetCOUNT64();
		int result = tcm2_unified_file_sm3_hash(path, digest);
		hash_end = CPU_GetCOUNT64();
		hash_cycles = hash_end - hash_start;
		hash_time_sec = hash_cycles / (tgt_pipefreq() / 2);

		if (result == 0) {
			// 动态确定文件类型：支持 vmlinux 和 vmlinuz
			const char *file_type = "vmlinux"; // 默认值
			if (strstr(path, "vmlinuz") != NULL) {
				file_type = "vmlinuz";
			} else if (strstr(path, "vmlinux") != NULL) {
				file_type = "vmlinux";
			}

			printf("LS2_SE: %s hash calculation successful\n", file_type);
			printf("LS2_SE: Calculation time: %lus\n", hash_time_sec);

			u32 nv_index = TCM2_METRIC_VMLINUZ;
			int nv_result = tcm2_metric_store_or_verify(nv_index, digest);
			if (nv_result != 0) {
				printf("LS2_SE: %s hash operation failed, error code: %d\n", file_type, nv_result);
				if(STOP_ON_MEASURE_FAILURE){
					close(bootfd);
					return -1;
				}
			}else{
				printf("LS2_SE: %s security measurement completed\n", file_type);
			}
		} else {
			// 动态确定文件类型用于错误信息
			const char *file_type = "vmlinux"; // 默认值
			if (strstr(path, "vmlinuz") != NULL) {
				file_type = "vmlinuz";
			} else if (strstr(path, "vmlinux") != NULL) {
				file_type = "vmlinux";
			}

			printf("LS2_SE: %s hash calculation failed\n", file_type);
			if(STOP_ON_MEASURE_FAILURE){
				close(bootfd);
				return -1;
			}
		}
	}
#endif

#ifdef HAVE_FLASH
	if (flags & FFLAG) {
		tgt_flashinfo(flashaddr, &flashsize);
		if (flashsize == 0) {
			printf ("No FLASH at given address\n");
			return 0;
		}
		/* any loaded program will be trashed... */
		flags &= ~(SFLAG | BFLAG | EFLAG);
		flags |= NFLAG;		/* don't bother with symbols */
		/*
		 * Recalculate any offset given on command line.
		 * Addresses should be 0 based, so a given offset should be
		 * the actual load address of the file.
		 */
		offset = (unsigned long)heaptop - offset;
#if BYTE_ORDER == LITTLE_ENDIAN
		bootbigend = 0;
#else
		bootbigend = 1;
#endif
	}
#endif
	dl_initialise (offset, flags);

	fprintf(stderr, "Loading file: %s ", path);
	errno = 0;
	n = 0;

	if (flags & RFLAG) {
		ExecId id;

		id = getExec("bin");
		if (id != NULL) {
			ep = exec (id, bootfd, buf, &n, flags);
		}
	} else {
		ep = exec(NULL, bootfd, buf, &n, flags);
	}

	close(bootfd);
	putc('\n', stderr);

	if (ep == -1) {
		fprintf(stderr, "%s: boot failed\n", path);
		return -3;
	}

	if (ep == -2) {
		fprintf(stderr, "%s: invalid file format\n", path);
		return -4;
	}

	if (!(flags & (FFLAG|YFLAG))) {
		printf("Entry address is %llx\n", ep);
		/* Flush caches if they are enabled */
		if (md_cachestat())
			flush_cache(DCACHE | ICACHE, NULL);
		md_setpc(NULL, ep);
		if (!(flags & SFLAG)) {
			dl_setloadsyms();
		}
	}
#ifdef HAVE_FLASH
	if (flags & FFLAG) {
		extern long dl_minaddr;
		extern long dl_maxaddr;
		if (flags & WFLAG)
			bootbigend = !bootbigend;
		tgt_flashprogram ((void *)flashaddr,		/* address */
				dl_maxaddr - dl_minaddr,	/* size */
				(void *)heaptop,		/* load */
				bootbigend);
	}
#endif
	return 0;
}

static	unsigned long rd_start;
static	unsigned int rd_size;
static	int execed;

int boot_initrd(const char* path, unsigned long rdstart,int flags)
{
	char buf[DLREC+1] = {0};
	int bootfd;
	int n = 0;
	ExecId id;

	rd_start = rdstart;
	rd_size = 0;

#if defined(TCM2) && defined(LS2_SE)
#if MEASURE_CRITICAL_FILES
	// 关键文件度量策略
	printf("LS2_SE: Executing critical file measurement policy\n");
	printf("LS2_SE: Starting critical file measurement...\n");

	int critical_result = tcm2_measure_critical_files(0, NULL);
	if (critical_result != 0) {
		printf("LS2_SE: Critical file measurement failed, error code: %d\n", critical_result);
		if (STOP_ON_MEASURE_FAILURE) {
			printf("LS2_SE: Critical file measurement failed, terminating boot\n");
			return -1;
		} else {
			printf("LS2_SE: Critical file measurement failed, but continuing boot\n");
		}
	} else {
		printf("LS2_SE: Critical file measurement completed\n");
	}

#else
	// 文件系统镜像度量策略（仅在关键文件度量关闭时执行）
	unsigned char digest[TCM2_DIGEST_LEN];
	unsigned long long hash_start, hash_end, hash_cycles;
	unsigned long hash_time_sec;
	printf("============LS2_SE: filesystem measurement for %s=============\n", path);
	hash_start = CPU_GetCOUNT64();
	int result = tcm2_unified_file_sm3_hash(path, digest);
	hash_end = CPU_GetCOUNT64();
	hash_cycles = hash_end - hash_start;
	hash_time_sec = hash_cycles / (tgt_pipefreq() / 2);
	if (result == 0) {
		printf("LS2_SE: initrd hash calculation successful\n");
		printf("LS2_SE: Calculation time: %lus\n", hash_time_sec);
		printf("LS2_SE: Detected filesystem file, performing security check\n");
		u32 nv_index = TCM2_METRIC_FILESYSTEM;
		const char *file_type = "filesystem";
		int nv_result = tcm2_metric_store_or_verify(nv_index, digest);
		if (nv_result != 0) {
			printf("LS2_SE: %s hash operation failed, error code: %d\n", file_type, nv_result);
			if(STOP_ON_MEASURE_FAILURE){
				close(bootfd);
				return -1;
			}
		}else{
			printf("LS2_SE: initrd security measurement completed\n");
		}
	} else {
		printf("LS2_SE: initrd hash calculation failed\n");
		if(STOP_ON_MEASURE_FAILURE){
			close(bootfd);
			return -1;
		}
	}
#endif
#endif

	printf("Loading initrd image %s", path);

	if ((bootfd = open (path, O_RDONLY | O_NONBLOCK)) < 0) {
		perror (path);
		return -1;
	}

	dl_initialise (rd_start, flags);

	id = getExec("bin");
	if (id != NULL) {
		exec (id, bootfd, buf, &n, flags);
		rd_size = (dl_maxaddr - dl_minaddr);
		execed = 1;
	} else {
		printf("[error] this pmon can't load bin file!");
		return -2;
	}
	close(bootfd);
#ifdef INITRD_DEBUG
	printf("rd_start %lx, rd_size %x\n", rd_start, rd_size);
#endif
	return 0;
}

int initrd_execed(void)
{
	return execed;
}

unsigned long get_initrd_start(void)
{
	return rd_start;
}

unsigned int get_initrd_size(void)
{
	return rd_size;
}

