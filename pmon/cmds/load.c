/* $Id: load.c,v 1.1.1.1 2006/09/14 01:59:08 root Exp $ */
/*
 * Copyright (c) 2002 Opsycon AB  (www.opsycon.se)
 * Copyright (c) 2002 <PERSON><PERSON>  (www.lindergren.com)
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *	This product includes software developed by Opsycon AB.
 *	This product includes software developed by <PERSON><PERSON>.
 * 4. The name of the author may not be used to endorse or promote products
 *    derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 */
/*  This code is based on code made freely available by Algorithmics, UK */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/param.h>
#include <sys/types.h>
#include <sys/errno.h>
#include <sys/endian.h>
#include <sys/malloc.h>

#include <pmon.h>
#include <exec.h>
#include <pmon/loaders/loadfn.h>
#include "flash.h"
#if (NMOD_FLASH_AMD + NMOD_FLASH_INTEL + NMOD_FLASH_SST) == 0
#ifdef HAVE_FLASH
#undef HAVE_FLASH
#endif
#else
#define HAVE_FLASH
#endif

#ifdef __loongarch__
#include <machine/cpu.h>
#endif

#ifdef LS2_SE
#include <time.h>
#include <sys/dev/tcm/tcm2_sm3.h>
#include <sys/dev/tcm/tcm2_metric.h>
#include <tcm-v2.h>
#include <tcm-common.h>
#include <machine/cpu.h>
#endif

extern int errno;                       /* global error number */
extern char *heaptop;

static int	bootfd;
static int	bootbigend;

int cmd_nload __P((int, char *[]));
static int nload __P((int, char **));

/* ------------------------------------------------------- */

const Optdesc         cmd_nload_opts[] =
{
	{"-s", "don't clear old symbols"},
	{"-b", "don't clear breakpoints"},
	{"-e", "don't clear exception handlers"},
	{"-a", "don't add offset to symbols"},
	{"-t", "load at top of memory"},
	{"-i", "ignore checksum errors"},
#ifdef HAVE_FLASH
	{"-f flash_addr -o load_addr offsetr", ""},
#endif
	{"-n", "don't load symbols"},
	{"-y", "only load symbols"},
	{"-v", "verbose messages"},
	{"-w", "reverse endianness"},
	{"-k", "prepare for kernel symbols"},
	{"-o<offs>", "load offset"},
	{"-r", "load raw file"},
	{"path", "path and filename"},
    {"\n Most frequenty cmds about load:\
	\n\tload from fat_usb:load /dev/fat/usb0/vmlinuxboot\
	\n\tload from tftp server:load tftp://*********/vmlinux\
	\n\tupdate bios: load -r -f 0xbfc00000 tftp://*********/gzrom.bin\n"},
	{0}
};

//	\n\tload kernel from usb disk: load /dev/fs/ext2@usb0/boot/vmlinux\

unsigned long long dl_loffset;
char *dl_Oloadbuffer;
unsigned long elf_address;

unsigned long long strtoull(const char *nptr,char **endptr,int base);
static int
nload (argc, argv)
	int argc;
	char **argv;
{
	char path[256];
	static char buf[2048];
	char error_path[] = "load: not enough arguments!";
	long ep;
	int n;
	extern int optind;
	extern char *optarg;
	int c, err;
	int flags;
	unsigned long offset;
#ifdef HAVE_FLASH
	void	    *flashaddr = 0;
	size_t	    flashsize;
#endif

	flags = 0;
	optind = err = 0;
	offset = 0;
	while ((c = getopt (argc, argv, "sbeatif:nrvwyko:O:")) != EOF) {
		switch (c) {
			case 's':
				flags |= SFLAG; break;
			case 'b':
				flags |= BFLAG; break;
			case 'e':
				flags |= EFLAG; break;
			case 'a':
				flags |= AFLAG; break;
			case 't':
				flags |= TFLAG; break;
			case 'i':
				flags |= IFLAG; break;
#ifdef HAVE_FLASH
			case 'f':
				if (!get_rsa_reg (&flashaddr, optarg)) {
					err++;
				}
				if (flashaddr < UNCACHED_MEMORY_ADDR)
					flashaddr = (u_int64_t)flashaddr  | UNCACHED_MEMORY_ADDR;
				flags |= FFLAG; break;
#endif
#if notyet
			case 'u':
				flashaddr = (void *)BOOTROMBASE;
				flags |= UFLAG;
				flags |= FFLAG; break;
#endif
			case 'n':
				flags |= NFLAG; break;
			case 'y':
				flags |= YFLAG; break;
			case 'v':
				flags |= VFLAG; break;
			case 'w':
				flags |= WFLAG; break;
			case 'k':
				flags |= KFLAG; break;
			case 'r':
				flags |= RFLAG; break;
			case 'o':
				if (!get_rsa_reg (&offset, optarg)) {
					err++;
				}
				break;
		  case 'O': dl_loffset=strtoull(optarg,0,0);
					flags|=OFLAG;
					break;
			default:
				err++;
				break;
		}
	}

	if (err) {
		return EXIT_FAILURE;
	}

	if (optind < argc) {
		strcpy(path, argv[optind++]);
	} 
	else {
		strcpy(path, error_path);//"load" command is not followed path
		printf("boot what?\n");
	}

	fill_path(path);
	if (path == NULL)
		return EXIT_FAILURE;
	if ((bootfd = open (path, O_RDONLY | O_NONBLOCK)) < 0) {
		perror (path);
		return EXIT_FAILURE;
	}

#if defined(TCM2) && defined(LS2_SE)
	// 检查是否为 vmlinux/vmlinuz 文件，对两者都进行度量
	{
		const char *filename = strrchr(path, '/');
		if (filename)
			filename++;
		else
			filename = path;

		if (strstr(filename, "vmlinuz") != NULL || strstr(filename, "vmlinux") != NULL) {
			unsigned char digest[TCM2_DIGEST_LEN];
			unsigned long long hash_start, hash_end, hash_cycles;
			unsigned long hash_time_us;

			printf("LS2_SE: Starting security measurement for vmlinuz: %s\n", path);

			hash_start = CPU_GetCOUNT64();
			int result = tcm2_unified_file_sm3_hash(path, digest);

			hash_end = CPU_GetCOUNT64();
			hash_cycles = hash_end - hash_start;
			hash_time_us = hash_cycles / (tgt_pipefreq() / 2 / 1000000);

			if (result == 0) {
				printf("LS2_SE: vmlinuz 哈希计算成功\n");
				printf("LS2_SE: 计算时间：%lu 微秒 (%lu.%03lu 毫秒)\n",
					   hash_time_us, hash_time_us / 1000, hash_time_us % 1000);

				u32 nv_index = TCM2_METRIC_VMLINUZ;
				const char *file_type = "vmlinuz";
				int nv_result = tcm2_metric_store_or_verify(nv_index, digest);
				if (nv_result != 0){
					printf("LS2_SE: %s 哈希操作失败，错误码：%d\n", file_type, nv_result);
					close(bootfd);
					return EXIT_FAILURE;
				}else{
					printf("LS2_SE: vmlinuz 安全度量完成\n");
				}
			}else{
				printf("LS2_SE: vmlinuz 哈希计算失败\n");
				close(bootfd);
				return EXIT_FAILURE;
			}
		}
	}
#endif

#ifdef HAVE_FLASH
	if (flags & FFLAG) {
		tgt_flashinfo (flashaddr, &flashsize);
		if (flashsize == 0) {
			printf ("No FLASH at given address\n");
			return 0;
		}
		/* any loaded program will be trashed... */
		flags &= ~(SFLAG | BFLAG | EFLAG);
		flags |= NFLAG;		/* don't bother with symbols */
		/*
		 * Recalculate any offset given on command line.
		 * Addresses should be 0 based, so a given offset should be
		 * the actual load address of the file.
		 */
		offset = (unsigned long)heaptop - offset;
#if BYTE_ORDER == LITTLE_ENDIAN
		bootbigend = 0;
#else
		bootbigend = 1;
#endif
	}
#endif

	dl_initialise (offset, flags);

	fprintf (stderr, "Loading file: %s ", path);
	errno = 0;
	n = 0;

	if (flags & RFLAG) {
	   ExecId id;

	   id = getExec("bin");
	   if (id != NULL) {
		   ep = exec (id, bootfd, buf, &n, flags);
	   }
	} else {
		if(flags&OFLAG){
		dl_Oloadbuffer=malloc(0x1000);
		if(dl_Oloadbuffer)
		 {
		  ep = exec (NULL, bootfd, buf, &n, flags);
		  free(dl_Oloadbuffer);
		 }
		}
		else ep = exec (NULL, bootfd, buf, &n, flags);
	}

	close (bootfd);
	putc ('\n', stderr);

	if (ep == -1) {
		fprintf (stderr, "%s: boot failed\n", path);
		return EXIT_FAILURE;
	}

	if (ep == -2) {
		fprintf (stderr, "%s: invalid file format\n", path);
		return EXIT_FAILURE;
	}

	if (!(flags & (FFLAG|YFLAG))) {
		printf ("Entry address is %08x\n", elf_address=ep);
		/* Flush caches if they are enabled */
		if (md_cachestat())
			flush_cache (DCACHE | ICACHE, NULL);
		md_setpc(NULL, ep);
		if (!(flags & SFLAG)) {
		    dl_setloadsyms ();
		}
	}
#ifdef HAVE_FLASH
	if (flags & FFLAG) {
		extern long dl_minaddr;
		extern long dl_maxaddr;
		if (flags & WFLAG)
			bootbigend = !bootbigend;
#ifdef SST25VF032B
		tgt_flashprogram_update_rom ((void *)flashaddr,
				dl_maxaddr - dl_minaddr,
				(void *)heaptop,
				bootbigend);
#else
		tgt_flashprogram ((void *)flashaddr, 	   	/* address */
				dl_maxaddr - dl_minaddr, 	/* size */
				(void *)heaptop,		/* load */
				bootbigend);
#endif
	}
#endif
	return EXIT_SUCCESS;
}

int
cmd_nload (argc, argv)
    int argc; char **argv;
{
    int ret;
    ret = spawn ("load", nload, argc, argv);
    return (ret & ~0xff) ? 1 : (signed char)ret;
}

/*
 *  Command table registration
 *  ==========================
 */
static const Cmd Cmds[] =
{
   {"Boot and Load"},
   {"load",	"[-beastifr][-o offs]",
   cmd_nload_opts,
   "load file",
   cmd_nload, 1, 16, 0},
   {0, 0}
};

static void init_cmd __P((void)) __attribute__ ((constructor));

static void
   init_cmd()
{
   cmdlist_expand(Cmds, 1);
}
