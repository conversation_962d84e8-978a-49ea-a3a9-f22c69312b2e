#include "tcm2_sm3.h"

#include <TcmTypes.h>
#include <string.h>
#include <sys/malloc.h>
#include <tcm-common.h>
#include <tcm-v2.h>
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>

#include "tcm-utils.h"
#include "tcm2_internal.h"
#include "tcm2_mailbox.h"
/*
 * 32-bit integer manipulation macros (big endian)
 */
#ifndef GET_ULONG_BE
#define GET_ULONG_BE(n, b, i)                                                                                     \
    {                                                                                                             \
        (n) = ((u32)(b)[(i)] << 24) | ((u32)(b)[(i) + 1] << 16) | ((u32)(b)[(i) + 2] << 8) | ((u32)(b)[(i) + 3]); \
    }
#endif

#ifndef PUT_ULONG_BE
#define PUT_ULONG_BE(n, b, i)           \
    {                                   \
        (b)[(i)] = (u8)((n) >> 24);     \
        (b)[(i) + 1] = (u8)((n) >> 16); \
        (b)[(i) + 2] = (u8)((n) >> 8);  \
        (b)[(i) + 3] = (u8)((n));       \
    }
#endif

void gm_sm3_init(struct sm3_context *ctx) {
    ctx->total = 0;
    ctx->slen = 32;
    ctx->state[0] = 0x7380166F;
    ctx->state[1] = 0x4914B2B9;
    ctx->state[2] = 0x172442D7;
    ctx->state[3] = 0xDA8A0600;
    ctx->state[4] = 0xA96F30BC;
    ctx->state[5] = 0x163138AA;
    ctx->state[6] = 0xE38DEE4D;
    ctx->state[7] = 0xB0FB0E4E;

    return;
}

static void sm3_process(struct sm3_context *ctx, u8 data[64]) {
    u32 SS1, SS2, TT1, TT2, W[68], W1[64];
    u32 A, B, C, D, E, F, G, H;
    u32 T[64];
    u32 Temp1, Temp2, Temp3, Temp4, Temp5;
    u32 j;
#ifdef _DEBUG
    int i;
#endif

    for(j = 0; j < 16; j++)
        T[j] = 0x79CC4519;
    for(j = 16; j < 64; j++)
        T[j] = 0x7A879D8A;

    GET_ULONG_BE(W[0], data, 0);
    GET_ULONG_BE(W[1], data, 4);
    GET_ULONG_BE(W[2], data, 8);
    GET_ULONG_BE(W[3], data, 12);
    GET_ULONG_BE(W[4], data, 16);
    GET_ULONG_BE(W[5], data, 20);
    GET_ULONG_BE(W[6], data, 24);
    GET_ULONG_BE(W[7], data, 28);
    GET_ULONG_BE(W[8], data, 32);
    GET_ULONG_BE(W[9], data, 36);
    GET_ULONG_BE(W[10], data, 40);
    GET_ULONG_BE(W[11], data, 44);
    GET_ULONG_BE(W[12], data, 48);
    GET_ULONG_BE(W[13], data, 52);
    GET_ULONG_BE(W[14], data, 56);
    GET_ULONG_BE(W[15], data, 60);

#ifdef _DEBUG
    printf("Message with padding:\n");
    for(i = 0; i < 8; i++)
        printf("%08x ", W[i]);
    printf("\n");
    for(i = 8; i < 16; i++)
        printf("%08x ", W[i]);
    printf("\n");
#endif

#define FF0(x, y, z) ((x) ^ (y) ^ (z))
#define FF1(x, y, z) (((x) & (y)) | ((x) & (z)) | ((y) & (z)))

#define GG0(x, y, z) ((x) ^ (y) ^ (z))
#define GG1(x, y, z) (((x) & (y)) | ((~(x)) & (z)))

#define SHL(x, n) (((x) & 0xFFFFFFFF) << n)
#define ROTL(x, n) (SHL((x), n) | ((x) >> (32 - n)))

#define P0(x) ((x) ^ ROTL((x), 9) ^ ROTL((x), 17))
#define P1(x) ((x) ^ ROTL((x), 15) ^ ROTL((x), 23))

    for(j = 16; j < 68; j++) {
        // W[j] = P1( W[j-16] ^ W[j-9] ^ ROTL(W[j-3],15)) ^ ROTL(W[j - 13],7 ) ^ W[j-6];
        // Why thd release's result is different with the debug's ?
        // Below is okay. Interesting, Perhaps VC6 has a bug of Optimizaiton.

        Temp1 = W[j - 16] ^ W[j - 9];
        Temp2 = ROTL(W[j - 3], 15);
        Temp3 = Temp1 ^ Temp2;
        Temp4 = P1(Temp3);
        Temp5 = ROTL(W[j - 13], 7) ^ W[j - 6];
        W[j] = Temp4 ^ Temp5;
    }

#ifdef _DEBUG
    printf("Expanding message W0-67:\n");
    for(i = 0; i < 68; i++) {
        printf("%08x ", W[i]);
        if(((i + 1) % 8) == 0)
            printf("\n");
    }
    printf("\n");
#endif

    for(j = 0; j < 64; j++) {
        W1[j] = W[j] ^ W[j + 4];
    }

#ifdef _DEBUG
    printf("Expanding message W'0-63:\n");
    for(i = 0; i < 64; i++) {
        printf("%08x ", W1[i]);
        if(((i + 1) % 8) == 0)
            printf("\n");
    }
    printf("\n");
#endif

    A = ctx->state[0];
    B = ctx->state[1];
    C = ctx->state[2];
    D = ctx->state[3];
    E = ctx->state[4];
    F = ctx->state[5];
    G = ctx->state[6];
    H = ctx->state[7];
#ifdef _DEBUG
    printf("j     A       B        C         D         E        F        G       H\n");
    printf("   %08x %08x %08x %08x %08x %08x %08x %08x\n", A, B, C, D, E, F, G, H);
#endif

    for(j = 0; j < 16; j++) {
        SS1 = ROTL((ROTL(A, 12) + E + ROTL(T[j], j)), 7);
        SS2 = SS1 ^ ROTL(A, 12);
        TT1 = FF0(A, B, C) + D + SS2 + W1[j];
        TT2 = GG0(E, F, G) + H + SS1 + W[j];
        D = C;
        C = ROTL(B, 9);
        B = A;
        A = TT1;
        H = G;
        G = ROTL(F, 19);
        F = E;
        E = P0(TT2);
#ifdef _DEBUG
        printf("%02d %08x %08x %08x %08x %08x %08x %08x %08x\n", j, A, B, C, D, E, F, G, H);
#endif
    }

    for(j = 16; j < 64; j++) {
        SS1 = ROTL((ROTL(A, 12) + E + ROTL(T[j], j)), 7);
        SS2 = SS1 ^ ROTL(A, 12);
        TT1 = FF1(A, B, C) + D + SS2 + W1[j];
        TT2 = GG1(E, F, G) + H + SS1 + W[j];
        D = C;
        C = ROTL(B, 9);
        B = A;
        A = TT1;
        H = G;
        G = ROTL(F, 19);
        F = E;
        E = P0(TT2);
#ifdef _DEBUG
        printf("%02d %08x %08x %08x %08x %08x %08x %08x %08x\n", j, A, B, C, D, E, F, G, H);
#endif
    }

    ctx->state[0] ^= A;
    ctx->state[1] ^= B;
    ctx->state[2] ^= C;
    ctx->state[3] ^= D;
    ctx->state[4] ^= E;
    ctx->state[5] ^= F;
    ctx->state[6] ^= G;
    ctx->state[7] ^= H;
#ifdef _DEBUG
    printf("   %08x %08x %08x %08x %08x %08x %08x %08x\n", ctx->state[0], ctx->state[1], ctx->state[2], ctx->state[3], ctx->state[4], ctx->state[5], ctx->state[6], ctx->state[7]);
#endif
}

u32 gm_sm3_update(struct sm3_context *ctx, u8 *input, u32 ilen) {
    u32 fill;
    u32 left;

    printf("DEBUG: gm_sm3_update called with ilen=%d\n", ilen);

    if(ilen <= 0) {
        printf("ilen is error %d\n", ilen);
        return -1;
    }

    left = (u32)(ctx->total & 0x3F);
    fill = 64 - left;

    ctx->total += ilen;
    // ctx->total &= 0xFFFFFFFFFFFFFFFF;

    if(left && ilen >= fill) {
        memcpy((void *)(ctx->buffer + left), (void *)input, fill);
        sm3_process(ctx, ctx->buffer);
        input += fill;
        ilen -= fill;
        left = 0;
    }

    while(ilen >= 64) {
        sm3_process(ctx, input);
        input += 64;
        ilen -= 64;
    }

    if(ilen > 0)
        memcpy((void *)(ctx->buffer + left), (void *)input, ilen);

    printf("DEBUG: gm_sm3_update completed successfully\n");
    return 0;
}

static const u8 sm3_padding[64] =
    {
        0x80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

u32 gm_sm3_finish(struct sm3_context *ctx, u8 output[32]) {
    u32 last, padn;
    u32 high, low;
    u8 msglen[8];
    u32 *p = &ctx->total;

    printf("DEBUG: gm_sm3_finish called, ctx->total=%lu\n", ctx->total);

    high = (p[0] >> 29) | (p[1] << 3);
    low = (p[0] << 3);

    PUT_ULONG_BE(high, msglen, 0);
    PUT_ULONG_BE(low, msglen, 4);

    last = p[0] & 0x3F;
    padn = (last < 56) ? (56 - last) : (120 - last);

    gm_sm3_update(ctx, (u8 *)sm3_padding, padn);
    gm_sm3_update(ctx, msglen, 8);

    PUT_ULONG_BE(ctx->state[0], output, 0);
    PUT_ULONG_BE(ctx->state[1], output, 4);
    PUT_ULONG_BE(ctx->state[2], output, 8);
    PUT_ULONG_BE(ctx->state[3], output, 12);
    PUT_ULONG_BE(ctx->state[4], output, 16);
    PUT_ULONG_BE(ctx->state[5], output, 20);
    PUT_ULONG_BE(ctx->state[6], output, 24);
    PUT_ULONG_BE(ctx->state[7], output, 28);

    printf("DEBUG: gm_sm3_finish completed successfully\n");
    return 0;
}

void se_sm3_init(void *sm3_ctx) {
    struct sm3_context *ctx;
    ctx = (struct sm3_context *)sm3_ctx;

    if(!ctx) {
        printf("sm3 init addr is NULL\n");
        return -1;
    }

    gm_sm3_init(ctx);
}

u32 se_sm3_update(void *sm3_ctx, u32 ilen, u8 *idata) {
    struct sm3_context *ctx;
    ctx = (struct sm3_context *)sm3_ctx;

    if(!idata || !ctx) {
        printf("sm3 update addr is NULL\n");
        return -1;
    }

    return gm_sm3_update(ctx, idata, ilen);
}

u32 se_sm3_final(void *sm3_ctx, u8 odata[32]) {
    struct sm3_context *ctx;
    ctx = (struct sm3_context *)sm3_ctx;

    printf("DEBUG: se_sm3_final called\n");

    if(!odata || !ctx) {
        printf("sm3 final addr is NULL\n");
        return -1;
    }

    u32 result = gm_sm3_finish(ctx, odata);
    printf("DEBUG: gm_sm3_finish returned %d\n", result);
    return result;
}

#define IPAD 0x36
#define OPAD 0x5c
#define SM3_DIGEST_SIZE 32
#define SM3_BLOCK_SIZE 64
#define SM3_HMAC_SIZE (SM3_DIGEST_SIZE)
u32 se_sm3_hmac_init(void *hmac_ctx, u32 key_len, u8 *key) {
    u32 ret = RT_EOK;
    u32 i;

    tcm_print_data("hmac key", key, key_len);

    struct hmac_context *ctx;
    ctx = (struct hmac_context *)hmac_ctx;

    if(!key || !ctx) {
        printf("sm3 hmac init addr is NULL\n");
        return -1;
    }
    memset(ctx, 0, sizeof(struct hmac_context));

    if(key_len <= SM3_BLOCK_SIZE) {
        memcpy(ctx->key, key, key_len);
        memset(&ctx->key[key_len], 0, SM3_BLOCK_SIZE - key_len);
    } else {
        gm_sm3_init(&ctx->sm3_ctx);
        gm_sm3_update(&ctx->sm3_ctx, key, key_len);
        gm_sm3_finish(&ctx->sm3_ctx, ctx->key);
        memset(ctx->key + SM3_DIGEST_SIZE, 0, SM3_BLOCK_SIZE - SM3_DIGEST_SIZE);
    }

    for(i = 0; i < SM3_BLOCK_SIZE; i++) {
        ctx->key[i] ^= IPAD;
    }

    gm_sm3_init(&ctx->sm3_ctx);
    gm_sm3_update(&ctx->sm3_ctx, ctx->key, SM3_BLOCK_SIZE);
    return 0;
}

u32 se_sm3_hmac_update(void *hmac_ctx, u32 ilen, u8 *idata) {
    u32 ret = RT_EOK;
    struct hmac_context *ctx;
    ctx = (struct hmac_context *)hmac_ctx;

    tcm_print_data("hmac idata", idata, ilen);

    if(!idata || !ctx) {
        printf("sm3 hmac update addr is NULL\n");
        return -1;
    }
    gm_sm3_update(&ctx->sm3_ctx, idata, ilen);
    return ret;
}

u32 se_sm3_hmac_final(void *hmac_ctx, u8 odata[32]) {
    u32 i;

    struct hmac_context *ctx;
    ctx = (struct hmac_context *)hmac_ctx;

    if(!odata || !ctx) {
        printf("sm3 hmac final addr is NULL\n");
        return -1;
    }

    for(i = 0; i < SM3_BLOCK_SIZE; i++) {
        ctx->key[i] ^= (IPAD ^ OPAD);
    }

    gm_sm3_finish(&ctx->sm3_ctx, odata);
    gm_sm3_init(&ctx->sm3_ctx);
    gm_sm3_update(&ctx->sm3_ctx, ctx->key, SM3_BLOCK_SIZE);
    gm_sm3_update(&ctx->sm3_ctx, odata, SM3_DIGEST_SIZE);
    gm_sm3_finish(&ctx->sm3_ctx, odata);

    return 0;
}

int tcm2_hmac_sw_cmd(int argc, char **argv) {
    tcm2_check_debug_flag(&argc, &argv);

    if(argc >= 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [key] [message]\n", argv[0]);
        printf("Compute HMAC-SM3 using software algorithm\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [key]          : Optional - Key to use for HMAC (in quotes)\n");
        printf("                   If not provided, a default test key will be used\n");
        printf("  [message]      : Optional - Message to compute HMAC for (in quotes)\n");
        printf("                   If not provided, a default test message will be used\n");
        printf("Examples:\n");
        printf("  %s             : Use default key and message\n", argv[0]);
        printf("  %s \"MySecret\" : Use 'MySecret' as key and default message\n", argv[0]);
        printf("  %s \"MySecret\" \"Hello\" : Compute HMAC for 'Hello' using 'MySecret' as key\n", argv[0]);
        return 0;
    }

    struct hmac_context hmac_ctx;
    u8 *key;
    u32 key_len;
    u8 *input;
    u32 input_len;
    u8 output[32];

    u8 hmac_sm3_key[64] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
        0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
        0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20,
        0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
        0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30,
        0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
        0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F, 0x40};

    u8 hmac_sm3_data[16] = {
        0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,  // "abcdefgh"
        0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70   // "ijklmnop"
    };

    if(argc == 1) {
        key = hmac_sm3_key;
        key_len = 64;
        input = hmac_sm3_data;
        input_len = 16;
    } else if(argc == 2) {
        key = (u8 *)argv[1];
        key_len = strlen((char *)key);
        input = (u8 *)"This is a test message for HMAC-SM3.";
        input_len = strlen((char *)input);
    } else {
        key = (u8 *)argv[1];
        key_len = strlen((char *)key);
        input = (u8 *)argv[2];
        input_len = strlen((char *)input);
    }

    if(se_sm3_hmac_init(&hmac_ctx, key_len, key) != 0) {
        printf("HMAC initialization failed\n");
        return -1;
    }

    if(se_sm3_hmac_update(&hmac_ctx, input_len, input) != 0) {
        printf("HMAC update failed\n");
        return -1;
    }

    if(se_sm3_hmac_final(&hmac_ctx, output) != 0) {
        printf("HMAC finalization failed\n");
        return -1;
    }

    tcm_print_data("HMAC-SM3 output", output, 32);

    return ACK_SUCCESS;
}

u32 tcm2_hash_init(u16 hash_alg, u32* handle) {
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(14),
        tcm_u32(TCM2_CC_HashSequenceStart),
        tcm_u16(0),
        tcm_u16(hash_alg),
    };

    u8 response[RESPONSE_BUFFER_SIZE] = {0};
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);
    if(ret) {
        printf("tcm2_hash_init: tcm_sendrecv_command failed, ret = 0x%08x\n", ret);
        return ret;
    }

    *handle = tcm_read_u32(response + 10);
    return 0;
}

u32 tcm2_hash_update(u32 handle, u8 *data, u32 data_len) {
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(0), 
        tcm_u32(TCM2_CC_SequenceUpdate),
        tcm_u32(handle),

        tcm_u32(9),
        tcm_u32(TCM2_RS_PW),
        tcm_u16(0),
        0,
        tcm_u16(0),

        tcm_u16(data_len),
    };

    u32 offest = 29;
    memcpy(command + offest, data, data_len);
    offest += data_len;
    tcm_write_u32(command + 2, offest);

    u8 response[RESPONSE_BUFFER_SIZE] = {0};
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);
    if(ret)
        printf("tcm2_hash_update: tcm_sendrecv_command failed, ret = 0x%08x\n", ret);

    return ret;
}

u32 tcm2_hash_final(u32 handle, u8 *remain, u32 remain_len, u8 *digest) {
    TCMI_RH_HIERARCHY hierarchy = TCM2_RH_NULL;
    
    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(0),
        tcm_u32(TCM2_CC_SequenceComplete),
        tcm_u32(handle),

        tcm_u32(9),
        tcm_u32(TCM2_RS_PW),
        tcm_u16(0),
        0,
        tcm_u16(0),

        tcm_u16(remain_len),
    };

    u32 offest = 29;
    memcpy(command + offest, remain, remain_len);
    offest += remain_len;
    tcm_write_u32(command + offest, hierarchy);
    offest += 4;
    tcm_write_u32(command + 2, offest);

    u8 response[RESPONSE_BUFFER_SIZE] = {0};
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);
    if(ret) {
        printf("tcm2_hash_final: tcm_sendrecv_command failed, ret = 0x%08x\n", ret);
        return ret;
    }

    u32 resp_offset = 16;
    memcpy(digest, response + resp_offset, TCM2_DIGEST_LEN);
    return 0;
}

u32 tcm2_segmented_hash(u8 *data, u32 data_len, u8 *digest) {
    u32 handle = 0;
    u32 ret = 0;
    u32 processed = 0;

    ret = tcm2_hash_init(TCM2_ALG_SM3_256, &handle);
    if(ret) {
        printf("tcm2_segmented_hash: tcm2_hash_init failed, ret = 0x%08x\n", ret);
        return ret;
    }

    while(processed + SM3_HARD_CHUNK_SIZE < data_len) {
        ret = tcm2_hash_update(handle, data + processed, SM3_HARD_CHUNK_SIZE);
        if(ret) {
            printf("tcm2_segmented_hash: tcm2_hash_update failed, ret = 0x%08x\n", ret);
            return ret;
        }
        processed += SM3_HARD_CHUNK_SIZE;

        if((processed / SM3_HARD_CHUNK_SIZE) % 10 == 0) {
            printf(".");
            fflush(stdout);
            if((processed / SM3_HARD_CHUNK_SIZE) % 160 == 0) {
                printf("\n");
            }
        }
    }

    u32 remaining = data_len - processed;
    ret = tcm2_hash_final(handle, data + processed, remaining, digest);
    if(ret) {
        printf("tcm2_segmented_hash: tcm2_hash_final failed, ret = 0x%08x\n", ret);
        return ret;
    }

    if(processed > 0)
        printf("\n");

    return 0;
}

int tcm2_hashsequance_test_cmd(int argc, char *argv[]) {
    tcm2_check_debug_flag(&argc, &argv);

    u8 test_data[6144] = {1};
    u32 test_data_len = sizeof(test_data);
    u8 digest[32];
    u32 ret = 0;

    printf("Testing segmented hash with %d bytes of data\n", test_data_len);

    ret = tcm2_segmented_hash(test_data, test_data_len, digest);
    if(ret) {
        printf("tcm2_segmented_hash failed, ret = 0x%08x\n", ret);
        return ret;
    }

    tcm_print_data("Hash output", digest, 32);

    return 0;
}

u32 tcm2_hard_sm3(const u8 *data, u16 data_len, u8 *digest) {
    TCM2B_MAX_BUFFER sm3_plain;
    sm3_plain.size = data_len;
    u8 response[RESPONSE_BUFFER_SIZE] = {0};
    u32 resp_len = sizeof(response);
    u32 offset = 10;
    int ret;

    memcpy(sm3_plain.buffer, data, data_len);

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_NO_SESSIONS),
        tcm_u32(16 + 2 + data_len),
        tcm_u32(TCM2_CC_Hash)
    };

    ret = pack_byte_string(command, sizeof(command), "ws", offset, sm3_plain.size, offset + 2, sm3_plain.buffer, data_len);
    if(ret) {
        printf("tcm2_sm3: pack_byte_string failed (size)\n");
        return TCM_LIB_ERROR;
    }

    offset += data_len + 2;
    tcm_write_u16(command + offset, TCM2_ALG_SM3_256);
    offset += 2;
    tcm_write_u32(command + offset, TCM2_RH_NULL);

    ret = tcm_sendrecv_command(command, response, &resp_len);
    if(ret) {
        printf("tcm2_sm3: tcm_sendrecv_command failed, ret = 0x%08x\n", ret);
        return ret;
    }

    TCM2B_DIGEST outHash;
    u32 resp_offset = 10;
    ret = unpack_byte_string(response, resp_len, "w", resp_offset, &outHash.size);
    if(ret) {
        printf("Failed to unpack digest size\n");
        return ret;
    }

    // printf("Digest Size: %u bytes\n", outHash.size);
    resp_offset += 2;

    ret = unpack_byte_string(response, resp_len, "s", resp_offset, digest, outHash.size);
    if(ret) {
        printf("Failed to unpack digest\n");
        return ret;
    }
    return 0;
}

u32 tcm2_hard_hmac(u32 handle, u8 *data, uint16_t data_len, u8 odata[32]) {
    uint16_t pw_sz = 0;

    u8 command[COMMAND_BUFFER_SIZE] = {
        tcm_u16(TCM2_ST_SESSIONS),
        tcm_u32(27 + pw_sz + 2 + data_len + 2),
        tcm_u32(TCM2_CC_HMAC),
        tcm_u32(handle),

        tcm_u32(9 + pw_sz),
        tcm_u32(TCM2_RS_PW),
        tcm_u16(0),
        0,
        tcm_u16(pw_sz),
    };

    u32 offset = 27;
    tcm_write_u16(command + offset, data_len);
    offset += 2;

    memcpy(command + offset, data, data_len);
    offset += data_len;

    tcm_write_u16(command + offset, TCM2_ALG_SM3_256);

    u8 response[RESPONSE_BUFFER_SIZE];
    u32 resp_len = sizeof(response);
    u32 ret = tcm_sendrecv_command(command, response, &resp_len);

    if(ret != 0) {
        tcm_err("%s:%d Failed to execute HMAC, error code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    u32 resp_offset = 14;
    uint16_t hmac_size = tcm_read_u16(response + resp_offset);
    memcpy(odata, response + resp_offset + 2, hmac_size);

    return 0;
}

int tcm2_hash_cmd(int argc, char *argv[]) {
    u8 buffer[1024] = {0};
    u8 *input;
    u32 input_len;
    u8 digest[32];
    u8 *dynamic_buffer = NULL;
    unsigned long long hash_start, hash_end, hash_cycles;
    unsigned long hash_time_sec;
    unsigned long hash_rate_kbps;
    int show_rate = 0;
    int i, j;

    tcm2_check_debug_flag(&argc, &argv);

    for(i = 1; i < argc; i++) {
        if(strcmp(argv[i], "-r") == 0) {
            show_rate = 1;
            for(j = i; j < argc - 1; j++) {
                argv[j] = argv[j + 1];
            }
            argc--;
            break;
        }
    }

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [-r] [message|memory_address size]\n", argv[0]);
        printf("Compute SM3 hash of the provided message or data from memory\n");
        printf("Note: Data > 1024 bytes will automatically use segmented hash\n");
        printf("Options:\n");
        printf("  -r             : Show performance rate information\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [message]      : Optional - Message to hash (in quotes)\n");
        printf("                   If not provided, a default test message will be used\n");
        printf("  [memory_address]: Optional - Memory address to read data from (in hex format)\n");
        printf("  [size]         : Size of data to read from memory address (in bytes)\n");
        printf("Examples:\n");
        printf("  %s                       : Hash the default test message\n", argv[0]);
        printf("  %s -r \"Hello World\"       : Hash with rate info\n", argv[0]);
        printf("  %s 0x900000000b02cc00 32 : Read 32 bytes from memory address and hash\n", argv[0]);
        printf("  %s -r 0x900000000b02cc00 2048: Read 2048 bytes with rate info\n", argv[0]);
        return 0;
    }

    if(argc == 1) {
        input = (u8 *)"This is a test message for SM3 hashing.";
        input_len = strlen((char *)input);
    } else if(argc == 2) {
        input = (u8 *)argv[1];
        input_len = strlen((char *)input);
    } else if(argc == 3) {
        unsigned long long addr = strtoull(argv[1], NULL, 0);
        input_len = strtoul(argv[2], NULL, 0);
        if(input_len <= sizeof(buffer)) {
            memcpy(buffer, (void *)addr, input_len);
            input = buffer;
        } else {
            dynamic_buffer = (u8 *)kern_malloc(input_len, M_DEVBUF, M_WAITOK);
            if(!dynamic_buffer) {
                tcm_err("%s:%d Failed to allocate memory for %d bytes", __func__, __LINE__, input_len);
                return -1;
            }
            memcpy(dynamic_buffer, (void *)addr, input_len);
            input = dynamic_buffer;
        }
    } else {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(show_rate) {
        printf("=== TCM2 Hash Performance Test ===\n");
        if(input_len >= 1048576) {  // >= 1MB
            printf("Data size: %u bytes (%u.%02u MB)\n", input_len, input_len / 1048576, (input_len % 1048576) * 100 / 1048576);
        } else {
            printf("Data size: %u bytes (%u.%03u KB)\n", input_len, input_len / 1024, (input_len % 1024) * 1000 / 1024);
        }
    }

    if(show_rate) {
        hash_start = CPU_GetCOUNT64();
    }

    u32 ret;
    if(input_len > 1024) {
        if(show_rate) printf("Algorithm: Hardware SM3 (segmented hash)\n");
        ret = tcm2_segmented_hash(input, input_len, digest);
    } else {
        if(show_rate) printf("Algorithm: Hardware SM3 (direct hash)\n");
        ret = tcm2_hard_sm3(input, input_len, digest);
    }

    if(show_rate) {
        hash_end = CPU_GetCOUNT64();
        hash_cycles = hash_end - hash_start;
        hash_time_sec = hash_cycles / (tgt_pipefreq() / 2);
    }

    if(ret != 0) {
        tcm_err("%s:%d SM3 hash computation failed, code: 0x%08X", __func__, __LINE__, ret);
        if(dynamic_buffer) kern_free(dynamic_buffer, M_DEVBUF);
        return -1;
    }

    if(show_rate && hash_time_sec > 0) {
        hash_rate_kbps = (input_len) / (hash_time_sec * 1024);

        printf("Processing time: %lus\n", hash_time_sec);

        if(hash_rate_kbps >= 1024) {
            printf("Processing rate: %lu.%03lu MB/s\n",
                   hash_rate_kbps / 1024, (hash_rate_kbps % 1024) * 1000 / 1024);
        } else {
            printf("Processing rate: %lu KB/s\n", hash_rate_kbps);
        }
    } else if(show_rate && hash_time_sec == 0) {
        printf("Processing time: <1s\n");
    }

    tcm_print_data("SM3 hash", digest, TCM2_DIGEST_LEN);

    if(dynamic_buffer) {
        kern_free(dynamic_buffer, M_DEVBUF);
    }

    return ACK_SUCCESS;
}

int tcm2_hash_sw_cmd(int argc, char *argv[]) {
    u8 buffer[1024] = {0};
    u8 *input;
    u32 input_len;
    u8 digest[32];
    u8 *dynamic_buffer = NULL;
    unsigned long long hash_start, hash_end, hash_cycles;
    unsigned long hash_time_sec;
    unsigned long hash_rate_kbps;
    int show_rate = 0;
    int i, j;

    tcm2_check_debug_flag(&argc, &argv);

    for(i = 1; i < argc; i++) {
        if(strcmp(argv[i], "-r") == 0) {
            show_rate = 1;
            for(j = i; j < argc - 1; j++) {
                argv[j] = argv[j + 1];
            }
            argc--;
            break;
        }
    }

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s [-r] [message|memory_address size]\n", argv[0]);
        printf("Compute SM3 hash using SOFTWARE algorithm\n");
        printf("Options:\n");
        printf("  -r             : Show performance rate information\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  [message]      : Optional - Message to hash (in quotes)\n");
        printf("                   If not provided, a default test message will be used\n");
        printf("  [memory_address]: Optional - Memory address to read data from (in hex format)\n");
        printf("  [size]         : Size of data to read from memory address (in bytes)\n");
        printf("Examples:\n");
        printf("  %s                       : Hash the default test message\n", argv[0]);
        printf("  %s -r \"Hello World\"       : Hash with rate info\n", argv[0]);
        printf("  %s 0x900000000b02cc00 32 : Read 32 bytes from memory address and hash\n", argv[0]);
        printf("  %s -r 0x900000000b02cc00 2048: Read 2048 bytes with rate info\n", argv[0]);
        return 0;
    }

    if(argc == 1) {
        input = (u8 *)"This is a test message for SM3 hashing.";
        input_len = strlen((char *)input);
    } else if(argc == 2) {
        input = (u8 *)argv[1];
        input_len = strlen((char *)input);
    } else if(argc == 3) {
        unsigned long long addr = strtoull(argv[1], NULL, 0);
        input_len = strtoul(argv[2], NULL, 0);
        if(input_len <= sizeof(buffer)) {
            memcpy(buffer, (void *)addr, input_len);
            input = buffer;
        } else {
            dynamic_buffer = (u8 *)kern_malloc(input_len, M_DEVBUF, M_WAITOK);
            if(!dynamic_buffer) {
                tcm_err("%s:%d Failed to allocate memory for %d bytes", __func__, __LINE__, input_len);
                return -1;
            }
            memcpy(dynamic_buffer, (void *)addr, input_len);
            input = dynamic_buffer;
        }
    } else {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return -1;
    }

    if(show_rate) {
        printf("=== TCM2 Software Hash Performance Test ===\n");
        if(input_len >= 1048576) {  // >= 1MB
            printf("Data size: %u bytes (%u.%02u MB)\n", input_len, input_len / 1048576, (input_len % 1048576) * 100 / 1048576);
        } else {
            printf("Data size: %u bytes (%u.%03u KB)\n", input_len, input_len / 1024, (input_len % 1024) * 1000 / 1024);
        }
        printf("Algorithm: Software SM3\n");
    }

    if(show_rate) {
        hash_start = CPU_GetCOUNT64();
    }

    struct sm3_context sw_ctx;
    se_sm3_init(&sw_ctx);

    u32 ret = 0;
    u32 processed = 0;

    if(input_len > SM3_SOFT_CHUNK_SIZE) {
        if(show_rate) printf("Using chunked processing with %d byte chunks\n", SM3_SOFT_CHUNK_SIZE);

        while(processed + SM3_SOFT_CHUNK_SIZE < input_len) {
            ret = se_sm3_update(&sw_ctx, SM3_SOFT_CHUNK_SIZE, input + processed);
            if(ret != 0) {
                printf("SM3 update failed at chunk %d\n", processed / SM3_SOFT_CHUNK_SIZE);
                if(dynamic_buffer) kern_free(dynamic_buffer, M_DEVBUF);
                return -1;
            }
            processed += SM3_SOFT_CHUNK_SIZE;

            printf(".");
            fflush(stdout);
        }

        u32 remaining = input_len - processed;
        if(remaining > 0) {
            ret = se_sm3_update(&sw_ctx, remaining, input + processed);
            if(ret != 0) {
                printf("SM3 update failed for remaining data\n");
                if(dynamic_buffer) kern_free(dynamic_buffer, M_DEVBUF);
                return -1;
            }
        }

        // Print newline after progress dots if any were printed
        if(processed > 0) {
            printf("\n");
        }
    } else {
        if(show_rate) printf("Using direct processing\n");
        ret = se_sm3_update(&sw_ctx, input_len, input);
    }

    if(ret == 0) {
        ret = se_sm3_final(&sw_ctx, digest);
    }

    if(show_rate) {
        hash_end = CPU_GetCOUNT64();
        hash_cycles = hash_end - hash_start;
        hash_time_sec = hash_cycles / (tgt_pipefreq() / 2);
    }

    if(ret != 0) {
        tcm_err("%s:%d Software SM3 hash computation failed, code: 0x%08X", __func__, __LINE__, ret);
        if(dynamic_buffer) kern_free(dynamic_buffer, M_DEVBUF);
        return -1;
    }

    if(show_rate && hash_time_sec > 0) {
        hash_rate_kbps = (input_len) / (hash_time_sec * 1024);

        printf("Processing time: %lus\n", hash_time_sec);

        if(hash_rate_kbps >= 1024) {
            printf("Processing rate: %lu.%03lu MB/s\n",
                   hash_rate_kbps / 1024, (hash_rate_kbps % 1024) * 1000 / 1024);
        } else {
            printf("Processing rate: %lu KB/s\n", hash_rate_kbps);
        }
    } else if(show_rate && hash_time_sec == 0) {
        printf("Processing time: <1s\n");
    }

    tcm_print_data("SM3 hash", digest, TCM2_DIGEST_LEN);

    if(dynamic_buffer) {
        kern_free(dynamic_buffer, M_DEVBUF);
    }

    return ACK_SUCCESS;
}

int tcm2_hmac_cmd(int argc, char *argv[]) {
    tcm2_check_debug_flag(&argc, &argv);

    if(argc == 2 && (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0)) {
        printf("Usage: %s <key_handle> [message|memory_address size]\n", argv[0]);
        printf("Compute HMAC-SM3 of the provided message using the specified key\n");
        printf("Options:\n");
        printf("  --help, -h     : Display this help message\n");
        printf("  <key_handle>   : Handle of the key to use for HMAC (in hex format, e.g. 0x80000000)\n");
        printf("  [message]      : Optional - Message to compute HMAC for (in quotes)\n");
        printf("                   If not provided, a default test message will be used\n");
        printf("  [memory_address]: Optional - Memory address to read data from (in hex format)\n");
        printf("  [size]         : Size of data to read from memory address (in bytes)\n");
        printf("Examples:\n");
        printf("  %s 0x80000000                  : Compute HMAC of the default test message\n", argv[0]);
        printf("  %s 0x80000000 \"Hello World\"    : Compute HMAC of the string 'Hello World'\n", argv[0]);
        printf("  %s 0x80000000 0x900000000b02cc00 32 : Read 32 bytes from memory address and compute HMAC; Max size 1024\n", argv[0]);
        return 0;
    }

    if(argc < 2 || argc > 4) {
        tcm_err("Use --help or -h for more information to resolve errors\n");
        return 1;
    }

    u32 handle = 0;
    if(sscanf(argv[1], "0x%x", &handle) != 1) {
        tcm_err("%s:%d Invalid handle format. Use hexadecimal format (e.g., 0x80000000), code: 0x%08X", __func__, __LINE__, TCM2_RC_VALUE);
        return 1;
    }

    u8 buffer[1024] = {0};
    u8 *input;
    u32 input_len;
    u8 output[32];
    u32 ret;

    if(argc == 2) {
        input = (u8 *)"This is a test message for HMAC-SM3. ";
        input_len = strlen((char *)input);
    } else if(argc == 3) {
        input = (u8 *)argv[2];
        input_len = strlen((char *)input);
    } else if(argc == 4) {
        unsigned long long addr = strtoull(argv[2], NULL, 0);
        input_len = strtoul(argv[3], NULL, 0);
        if(input_len > sizeof(buffer)) {
            tcm_err("%s:%d Input data too large, maximum size is %d bytes", __func__, __LINE__, sizeof(buffer));
            return 1;
        }
        memcpy(buffer, (void *)addr, input_len);
        input = buffer;

        printf("Data read from memory:\n");
        for(int i = 0; i < input_len; i++) {
            printf("%02x ", buffer[i]);
            if((i + 1) % 16 == 0) printf("\n");
        }
        printf("\n");
    }

    ret = tcm2_hard_hmac(handle, input, input_len, output);
    if(ret != 0) {
        tcm_err("%s:%d HMAC operation failed, error code: 0x%08X", __func__, __LINE__, ret);
        return ret;
    }

    tcm_print_data("[DEBUG] HMAC Result", output, TCM2_DIGEST_LEN);

    return 0;
}

/* 统一 SM3 哈希计算接口 */
int tcm2_unified_file_sm3_hash(const char* file_path, u8* hash_output) {
    if (!file_path || !hash_output) {
        printf("Error: invalid SM3 hash parameters\n");
        return -1;
    }

    int fd = open(file_path, O_RDONLY);
    if (fd < 0) {
        printf("Error: cannot open file %s\n", file_path);
        return -1;
    }

    int bytes_read;
    unsigned long total_bytes = 0;
    unsigned int chunk_count = 0;

#if USE_HARDWARE_SM3
    unsigned char buffer[SM3_HARD_CHUNK_SIZE];  // 硬件 SM3 使用 1024 字节缓冲区
    u32 handle = 0;
    u32 result = tcm2_hash_init(TCM2_ALG_SM3_256, &handle);
    if (result) {
        printf("Error: hardware SM3 init failed 0x%08X\n", result);
        close(fd);
        return -1;
    }

    while ((bytes_read = read(fd, buffer, sizeof(buffer))) > 0) {
        total_bytes += bytes_read;
        chunk_count++;

        result = tcm2_hash_update(handle, buffer, bytes_read);
        if (result) {
            printf("Error: hardware SM3 update failed 0x%08X\n", result);
            close(fd);
            return -1;
        }
        printf("Processed %luKB %luB, Shard Size(%u)\n", total_bytes/1024, total_bytes%1024, SM3_HARD_CHUNK_SIZE);
    }

    close(fd);

    if (bytes_read < 0) {
        printf("Error: file read failed\n");
        return -1;
    }

    result = tcm2_hash_final(handle, NULL, 0, hash_output);
    if (result) {
        printf("Error: hardware SM3 final failed 0x%08X\n", result);
        return -1;
    }

#else
    unsigned char buffer[SM3_SOFT_CHUNK_SIZE];  // 软算法使用40960字节缓冲区
    struct sm3_context sw_ctx;
    se_sm3_init(&sw_ctx);

    while ((bytes_read = read(fd, buffer, sizeof(buffer))) > 0) {
        total_bytes += bytes_read;
        chunk_count++;

        // 添加调试信息
        printf("DEBUG: About to call se_sm3_update with bytes_read=%d\n", bytes_read);

        u32 update_result = se_sm3_update(&sw_ctx, bytes_read, buffer);

        printf("DEBUG: se_sm3_update returned %d\n", update_result);

        if (update_result != 0) {
            printf("Error: software SM3 update failed, bytes_read=%d, result=0x%08X\n", bytes_read, update_result);
            close(fd);
            return -1;
        }

        printf("Processed %luKB %luB, Shard Size(%u)\n", total_bytes/1024, total_bytes%1024, SM3_SOFT_CHUNK_SIZE);
    }

    printf("DEBUG: Exited while loop, bytes_read=%d, total_bytes=%lu\n", bytes_read, total_bytes);

    close(fd);

    if (bytes_read < 0) {
        printf("Error: file read failed\n");
        return -1;
    }

    printf("DEBUG: About to call se_sm3_final\n");
    u32 final_result = se_sm3_final(&sw_ctx, hash_output);
    printf("DEBUG: se_sm3_final returned %d\n", final_result);
#endif

    printf("SM3 hash completed: %lu bytes, %u chunks\n", total_bytes, chunk_count);
    tcm_print_data("SM3 hash", hash_output, 32);

    return 0;
}